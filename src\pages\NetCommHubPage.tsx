import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Network, Lock, Users, Zap, MessageSquare, Settings } from 'lucide-react';

function NetCommHubPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link to="/" className="flex items-center text-gray-600 hover:text-gray-900">
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Portfolio
          </Link>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <img
            src="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?auto=format&fit=crop&w=1600&h=600"
            alt="NetCommHub System"
            className="w-full h-96 object-cover"
          />
          
          <div className="p-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">NetCommHub - Group Communication System</h1>
            
            <div className="flex flex-wrap gap-2 mb-8">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Java</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Networking</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Distributed Systems</span>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">GUI</span>
            </div>

            <div className="prose max-w-none">
              <p className="text-lg text-gray-600 mb-8">
                A robust networked distributed system enabling group-based client-server communication with
                a graphical user interface. The system implements a coordinator-based architecture with
                fault tolerance and real-time messaging capabilities.
              </p>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Key Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <FeatureCard
                  icon={<Network className="w-6 h-6" />}
                  title="Distributed Architecture"
                  description="Robust client-server communication with coordinator-based design"
                />
                <FeatureCard
                  icon={<Lock className="w-6 h-6" />}
                  title="Fault Tolerance"
                  description="Automatic coordinator reassignment and error handling"
                />
                <FeatureCard
                  icon={<Users className="w-6 h-6" />}
                  title="Group Management"
                  description="Dynamic member discovery and role-based privileges"
                />
                <FeatureCard
                  icon={<MessageSquare className="w-6 h-6" />}
                  title="Real-time Messaging"
                  description="Direct and broadcast messaging capabilities"
                />
                <FeatureCard
                  icon={<Settings className="w-6 h-6" />}
                  title="Intuitive Interface"
                  description="Tab-based GUI with real-time updates"
                />
                <FeatureCard
                  icon={<Zap className="w-6 h-6" />}
                  title="High Performance"
                  description="Efficient message routing and delivery"
                />
              </div>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Technical Details</h2>
              <ul className="list-disc pl-6 text-gray-600 mb-8">
                <li>Java-based implementation with Swing GUI</li>
                <li>TCP/IP networking for reliable communication</li>
                <li>Thread-safe message handling</li>
                <li>Event-driven architecture</li>
                <li>Comprehensive error handling</li>
                <li>Efficient member discovery protocol</li>
                <li>Modular and extensible design</li>
              </ul>

              <h2 className="text-2xl font-bold text-gray-900 mb-4">Results</h2>
              <ul className="list-disc pl-6 text-gray-600">
                <li>Successfully tested with multiple concurrent users</li>
                <li>Robust handling of network failures</li>
                <li>Minimal latency in message delivery</li>
                <li>Positive user feedback on interface design</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      <div className="text-blue-600 mb-3">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}

export default NetCommHubPage;