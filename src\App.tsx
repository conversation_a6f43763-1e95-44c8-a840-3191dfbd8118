import React from 'react';
import { Routes, Route } from 'react-router-dom';
import HomePage from './pages/HomePage';
import XRayDetectionPage from './pages/XRayDetectionPage';
import NetCommHubPage from './pages/NetCommHubPage';
import StudyPlatformPage from './pages/StudyPlatformPage';
import TextSummarizationPage from './pages/TextSummarizationPage';

function App() {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />
      <Route path="/projects/xray-detection" element={<XRayDetectionPage />} />
      <Route path="/projects/netcommhub" element={<NetCommHubPage />} />
      <Route path="/projects/study-platform" element={<StudyPlatformPage />} />
      <Route path="/projects/text-summarization" element={<TextSummarizationPage />} />
    </Routes>
  );
}

export default App;