import React from 'react';
import { Linkedin, Mail, Code2, Database, Layout, Server, Braces, Globe, Award, Briefcase } from 'lucide-react';
import { Link } from 'react-router-dom';

function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Header/Hero Section */}
      <header className="bg-white shadow-sm">
        <div className="max-w-5xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <img
              className="w-32 h-32 rounded-full mx-auto mb-6 shadow-lg"
              src="https://res.cloudinary.com/dramsks0e/image/upload/v1750756231/avatar.png"
              alt="Nir Peretz - Software Engineer Profile Photo"
              width="128"
              height="128"
              loading="eager"
            />
            <h1 className="text-4xl font-bold text-gray-900 mb-2"><PERSON><PERSON></h1>
            <p className="text-xl text-gray-600 mb-6">Software Engineer</p>
            <div className="flex justify-center space-x-4">
              <a href="https://github.com/PeretzNiro" className="text-gray-600 hover:text-gray-900" aria-label="GitHub Profile">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="https://www.linkedin.com/in/nir-peretz/" className="text-gray-600 hover:text-gray-900">
                <Linkedin className="w-6 h-6" />
              </a>
              <a href="mailto:<EMAIL>" className="text-gray-600 hover:text-gray-900">
                <Mail className="w-6 h-6" />
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* About Section */}
      <section className="py-16 bg-white">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">About Me</h2>
          <p className="text-lg text-gray-600 leading-relaxed">
            I'm a passionate software engineer with 5+ years of experience building scalable web applications
            and distributed systems. I specialize in full-stack development with a focus on cloud-native
            solutions and modern JavaScript frameworks. When I'm not coding, you can find me contributing
            to open-source projects or mentoring aspiring developers.
          </p>
        </div>
      </section>

      {/* Employment Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Employment History</h2>
          <div className="space-y-12">
            <div className="relative pl-8 border-l-2 border-blue-500">
              <div className="absolute w-4 h-4 bg-blue-500 rounded-full -left-[9px] top-0"></div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center mb-2">
                  <Briefcase className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="text-xl font-semibold text-gray-900">Senior Software Engineer</h3>
                </div>
                <p className="text-blue-600 font-medium mb-2">TechCorp Solutions • 2021 - Present</p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Led development of cloud-native microservices architecture</li>
                  <li>Reduced system latency by 40% through optimization</li>
                  <li>Mentored junior developers and conducted code reviews</li>
                </ul>
              </div>
            </div>

            <div className="relative pl-8 border-l-2 border-blue-500">
              <div className="absolute w-4 h-4 bg-blue-500 rounded-full -left-[9px] top-0"></div>
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center mb-2">
                  <Briefcase className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="text-xl font-semibold text-gray-900">Full Stack Developer</h3>
                </div>
                <p className="text-blue-600 font-medium mb-2">InnovateTech • 2019 - 2021</p>
                <ul className="list-disc list-inside text-gray-600 space-y-2">
                  <li>Developed and maintained multiple client-facing applications</li>
                  <li>Implemented CI/CD pipelines reducing deployment time by 60%</li>
                  <li>Collaborated with UX team to improve user experience</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Certificates Section */}
      <section className="py-16 bg-white">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Certificates</h2>
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 flex items-center">
            <div className="bg-white rounded-full p-3 mr-6">
              <Award className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white mb-2">AWS Certified Cloud Practitioner</h3>
              <p className="text-blue-100">Issued by Amazon Web Services • 2023</p>
            </div>
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Technical Skills</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <SkillCard
              icon={<Code2 className="w-8 h-8" />}
              title="Frontend Development"
              skills={['React', 'TypeScript', 'Next.js', 'Tailwind CSS']}
            />
            <SkillCard
              icon={<Server className="w-8 h-8" />}
              title="Backend Development"
              skills={['Node.js', 'Python', 'Go', 'RESTful APIs']}
            />
            <SkillCard
              icon={<Database className="w-8 h-8" />}
              title="Databases"
              skills={['PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch']}
            />
            <SkillCard
              icon={<Layout className="w-8 h-8" />}
              title="DevOps"
              skills={['Docker', 'Kubernetes', 'AWS', 'CI/CD']}
            />
            <SkillCard
              icon={<Braces className="w-8 h-8" />}
              title="Programming Languages"
              skills={['JavaScript', 'TypeScript', 'Python', 'Go']}
            />
            <SkillCard
              icon={<Globe className="w-8 h-8" />}
              title="Other Skills"
              skills={['System Design', 'Agile', 'Git', 'Testing']}
            />
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section className="py-16 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Featured Projects</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Link to="/projects/xray-detection" className="block hover:transform hover:scale-105 transition-transform duration-200">
              <ProjectCard
                title="X-Ray Pneumonia Detection"
                description="AI-powered diagnostic tool using deep learning to automatically detect pneumonia from chest X-ray images with high accuracy."
                tags={['Python', 'TensorFlow', 'Deep Learning', 'Medical AI']}
                image="https://res.cloudinary.com/dramsks0e/image/upload/v1750755613/Medical-AI-X-ray_bcln1g.png"
              />
            </Link>
            <Link to="/projects/netcommhub" className="block hover:transform hover:scale-105 transition-transform duration-200">
              <ProjectCard
                title="NetCommHub"
                description="A robust networked distributed system for group-based client-server communication with a graphical user interface."
                tags={['Java', 'Networking', 'Distributed Systems', 'GUI']}
                image="https://images.unsplash.com/photo-**********-b99a580bb7a8?auto=format&fit=crop&w=400&h=240&q=80"
              />
            </Link>
            <Link to="/projects/study-platform" className="block hover:transform hover:scale-105 transition-transform duration-200">
              <ProjectCard
                title="AI Study Platform"
                description="An advanced educational platform that transforms lecture materials into summarized content and interactive quizzes using AI."
                tags={['React', 'TypeScript', 'AWS', 'AI/ML']}
                image="https://res.cloudinary.com/dramsks0e/image/upload/v1750755613/AI-driven-educational-platform_ufzllw.png"
              />
            </Link>
            <Link to="/projects/text-summarization" className="block hover:transform hover:scale-105 transition-transform duration-200">
              <ProjectCard
                title="DFO Text Summarization"
                description="Python implementation of extractive text summarization using Derivative-Free Optimization algorithms for scientific papers."
                tags={['Python', 'NLP', 'ML', 'Optimization']}
                image="https://images.unsplash.com/photo-1456324504439-367cee3b3c32?auto=format&fit=crop&w=400&h=240&q=80"
              />
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">© 2024 Nir Peretz. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
}

interface SkillCardProps {
  icon: React.ReactNode;
  title: string;
  skills: string[];
}

function SkillCard({ icon, title, skills }: SkillCardProps) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex items-center mb-4">
        <div className="text-blue-600 mr-3">{icon}</div>
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
      </div>
      <ul className="space-y-2">
        {skills.map((skill) => (
          <li key={skill} className="text-gray-600">{skill}</li>
        ))}
      </ul>
    </div>
  );
}

interface ProjectCardProps {
  title: string;
  description: string;
  tags: string[];
  image: string;
}

function ProjectCard({ title, description, tags, image }: ProjectCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <img
        src={image}
        alt={`${title} project screenshot`}
        className="w-full h-48 object-cover"
        width="400"
        height="192"
        loading="lazy"
        decoding="async"
      />
      <div className="p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">{description}</p>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <span
              key={tag}
              className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}

export default HomePage;